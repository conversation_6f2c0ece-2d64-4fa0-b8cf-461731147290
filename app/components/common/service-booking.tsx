import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { datadummy } from '@/constants/data-dummy';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Plus } from 'lucide-react';
import { Calendar as CalendarIcon } from 'lucide-react';

function ServiceBooking() {
  return (
    <Dialog>
      <form>
        <DialogTrigger asChild>
          <Button
            variant="default"
            className="h-[36px] w-[36px]  text-white  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent className="w-full max-w-[1000px] sm:max-w-[1000px]">
          <DialogHeader>
            <DialogTitle>Service</DialogTitle>
          </DialogHeader>
          <hr />
          <DialogTitle className="mt-3">Booking Information</DialogTitle>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-5 mt-3">
            <div className="grid gap-2">
              <Label htmlFor="orderid">Order ID</Label>
              <Input id="orderid" name="orderid" defaultValue="BE-000" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="username-1">Customer Name</Label>
              <Input id="username-1" name="username-1" defaultValue="John" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone-number">Phone Number</Label>
              <Input id="phone-number" name="phone-number" defaultValue="017 501 733" />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-5">
            <div className="grid gap-2">
              <Label htmlFor="dateTime">Date</Label>
              <div className="relative">
                <Input
                  id="dateTime"
                  name="dateTime"
                  type="datetime-local"
                  defaultValue="2023-01-01T12:00"
                  className="pr-10"
                />
                <CalendarIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="balcony">Address</Label>
              <Input id="balcony" name="balcony" defaultValue="Phnom Penh, Cambodia" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="type-of-service">Type of Service</Label>
              <Select>
                <SelectTrigger className="w-full max-w-[1000px] sm:max-w-[1200px]">
                  <SelectValue placeholder="Select Service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="deep cleaning">Deep Cleaning</SelectItem>
                  <SelectItem value="outside cleaning">Outside Cleaning</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-5">
            <div className="grid gap-2">
              <Label htmlFor="hour-of-service">Type of Service</Label>
              <Select>
                <SelectTrigger className="w-full max-w-[1000px] sm:max-w-[1200px]">
                  <SelectValue placeholder="Select Hour" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="washing">Washing</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="hour-of-service">Hour of Service</Label>
              <Select>
                <SelectTrigger className="w-full max-w-[1000px] sm:max-w-[1200px]">
                  <SelectValue placeholder="Select Hour" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Hour</SelectItem>
                  <SelectItem value="2">2 Hours</SelectItem>
                  <SelectItem value="3">3 Hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <br />

            <div className="sm:col-span-2">
              <DialogTitle>Service Details</DialogTitle>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
                {datadummy.map(({ id, label, checked }) => (
                  <label key={id} className="flex items-center gap-2">
                    <Checkbox id={id} defaultChecked={checked} />
                    <span>{label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-2">
            <a href="#" className="flex items-center text-sm text-blue-600 hover:underline">
              <Plus className="w-4 h-4 mr-1" />
              Add another service add-on
            </a>
          </div>

          <DialogTitle className="mt-4">Payment Information</DialogTitle>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-5">
            <div className="grid gap-2">
              <Label htmlFor="paymentStatus">Payment Status</Label>
              <Select>
                <SelectTrigger
                  id="paymentStatus"
                  name="paymentStatus"
                  className="w-full max-w-[1000px] sm:max-w-[1200px]"
                >
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Select>
                <SelectTrigger
                  id="paymentMethod"
                  name="paymentMethod"
                  className="w-full max-w-[1000px] sm:max-w-[1200px]"
                >
                  <SelectValue placeholder="Select Payment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aba">ABA KHQR</SelectItem>
                  <SelectItem value="acleda">ACLEDA</SelectItem>
                  <SelectItem value="wing">Wing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <hr />
          <DialogFooter className="mt-4">
            <DialogClose asChild></DialogClose>
            <Button type="submit">Create</Button>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}

export default ServiceBooking;
