import clsx from 'clsx';
import { MapsIcon } from 'hugeicons-react';
import React from 'react';

const OrderAddress: React.FC<{ userAddress?: UserAddressProps }> = ({ userAddress }) => {
  const openMap = () => {
    window.open(
      `https://www.google.com/maps/search/?api=1&query=${userAddress?.latitude},${userAddress?.longitude}`,
      '_blank'
    );
  };

  return (
    <div className="space-y-1 max-w-[60%]">
      <div className="flex justify-between items-center">
        <p className="text-[#707070] font-medium text-sm font-inter">Address</p>
        <button
          onClick={openMap}
          className={clsx(
            'flex items-center gap-1 text-sm text-primary hover:underline focus:outline-none',
            {
              hidden: !userAddress?.latitude || !userAddress?.longitude
            }
          )}
        >
          View Map
          <MapsIcon size={18} strokeWidth={1.5} color="#1964AD" />
        </button>
      </div>
      <p className="text-black font-medium text-sm font-inter">{userAddress?.address}</p>
    </div>
  );
};
export default OrderAddress;
