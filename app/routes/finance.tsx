import * as React from 'react';
import { Table, TableBody } from '@/components/ui/table';
import useTableState from '@/hooks/use-table-state';
import { dummyFinanceData } from '@/constants/data-dummy'; // <-- replace with your Finance dummy data
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/use-data-table-config';
import TableHeader from '@/components/data-table/data-table-header';
import TableRows from '@/components/data-table/table-rows';
import FinanceHeader from '@/components/data-table/finance-header'; // <-- updated header
import { financeColumns } from '@/components/data-table/finance-columns'; // <-- updated columns

export default function FinanceTable() {
  const tableState = useTableState();
  const table = useDataTableConfig(dummyFinanceData, financeColumns, tableState);
  const { dateRange, isCalendarOpen, setDateRange, setIsCalendarOpen } = tableState;

  const financeProps = {
    table,
    dateRange,
    setDateRange,
    isCalendarOpen,
    setIsCalendarOpen
  };

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <FinanceHeader {...financeProps} />
        <div className="flex min-h-0 overflow-hidden">
          <Table className="min-w-full">
            <TableHeader table={table} />
            <TableBody>
              <TableRows columns={financeColumns} table={table} />
            </TableBody>
          </Table>
        </div>
      </div>
      <TablePagination table={table} />
    </div>
  );
}
