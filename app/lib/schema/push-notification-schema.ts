import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

// Base schema for shared fields
const baseSchema = z.object({
  name: multiLangNameSchema,
  description: z.string().min(1, 'Description is required'),
  status: z.enum(['Active', 'Inactive', 'Pending'], {
    required_error: 'Status is required'
  }),
  targetUser: z.enum(['General User', 'Elderly'], {
    required_error: 'Target user is required'
  }),
  validFrom: z.string().min(1, 'Valid From date is required'),
  validTo: z.string().min(1, 'Valid To date is required')
});

// General notification schema → only base fields + notificationType
const GeneralSchema = baseSchema.extend({
  notificationType: z.literal('General')
});

// Promotion notification schema → base fields + notificationType + buddleType
const PromotionSchema = baseSchema.extend({
  notificationType: z.literal('Promotion'),
  buddleType: z.enum(['Bundle Deals - More Saves, More Services!', 'buy-more-pay-less'], {
    required_error: 'Buddle type is required'
  })
});

// Discriminated union based on notificationType
export const pushNotificationSchema = z.discriminatedUnion('notificationType', [
  GeneralSchema,
  PromotionSchema
]);

export type PushNotificationSchemaProps = z.infer<typeof pushNotificationSchema>;
