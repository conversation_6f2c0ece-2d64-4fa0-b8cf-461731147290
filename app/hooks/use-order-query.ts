import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { CONSTANTS } from '@/constants/constants';

type Props = {
  data: OrderListAttributes[];
  pagination: PaginationProps;
};

export default function useOrderQuery({ currentPage }: { currentPage: number }) {
  const apiFn = (): Promise<Props> => {
    return api.get(API_ENDPOINT.ORDERS, {
      params: {
        page: currentPage + 1,
        limit: CONSTANTS.LIMIT_PER_PAGE
      }
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.ORDERS, currentPage],
    queryFn: apiFn,
    placeholderData: keepPreviousData
  });

  return query;
}
