import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import ProfilePicker from '@/components/common/profile-picker';
import CustomSelect from '@/components/common/custom-select';
import DragDropFileUpload from '@/components/common/drag-drop-file-upload';
import { categorySchema, type CategorySchemaProps } from '@/lib/schema/category-schema';
import { useState } from 'react';
import DraggableComboboxPanel from '@/components/common/draggable/draggable-combobox-panel';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';
import { TaskInformationDialog } from '@/components/category/task-information/task-information-dialog';
import TaskInformationPanel from '@/components/category/task-information/task-information-panel';

export default function NewCategory() {
  const { t } = useTranslation();
  const [products, setProducts] = useState<DraggableComboBoxProps[]>([]);
  const [categoryAddOn, setCategoryAddOn] = useState<DraggableComboBoxProps[]>([]);
  const [open, setOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState<number | undefined>(undefined);

  const form = useForm<CategorySchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: {
        en: '',
        km: '',
        vn: '',
        zh: '',
        cn: ''
      },
      status: 'Active',
      attachments: [],
      taskInformation: []
    }
  });

  const fieldArray = useFieldArray({
    control: form.control,
    name: 'taskInformation'
  });

  const onSubmit = async (values: CategorySchemaProps) => {
    console.log('Form submitted with values:', values);
  };

  const handleClick = (index?: number) => {
    if (index !== undefined) {
      setSelectedIndex(Number(index));
    } else {
      setSelectedIndex(undefined);
    }
    setOpen(true);
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit, (err) => {
            console.log({ err });
          })}
        >
          <CustomHeader onSave={form.handleSubmit(onSubmit)} />
          <ContentWrapper>
            <div className="p-6 flex flex-row gap-6 items-baseline">
              <Card className="flex flex-1">
                <CardHeader className="gap-4">
                  <CardTitle>{t('categoryPage.details')}</CardTitle>
                  <ProfilePicker />
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-3">
                    <FormInputMultipleLanguages
                      form={form}
                      name="name"
                      label="Name"
                      placeholder="Name"
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Status</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder="Status"
                            data={[
                              { label: 'Active', value: 'Active' },
                              { label: 'Resigned', value: 'Resigned' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="mt-4">
                    <DragDropFileUpload placeholder="What's Included" />
                  </div>
                </CardContent>
              </Card>

              {/* want to include this section as well */}
              <div className="w-[368px] flex flex-col gap-4 h-full">
                <TaskInformationPanel
                  title={t('categoryPage.taskInformation')}
                  control={form.control}
                  name="taskInformation"
                  onClick={handleClick}
                  fieldArray={fieldArray}
                />

                {/* <DraggableInputPanel
              control={form.control}
              name="taskInformation"
              title={t('categoryPage.taskInformation')}
              data={tasks}
              onChange={setTasks}
            /> */}
                <DraggableComboboxPanel
                  title={t('categoryPage.product')}
                  buttonText={t('categoryPage.addProduct')}
                  data={products}
                  onChange={setProducts}
                />
                <DraggableComboboxPanel
                  title={t('categoryPage.categoryAddOn')}
                  buttonText={t('categoryPage.addCategoryAddOn')}
                  data={categoryAddOn}
                  onChange={setCategoryAddOn}
                />
              </div>
            </div>
          </ContentWrapper>
        </form>
      </Form>
      {open && (
        <TaskInformationDialog
          open={open}
          setOpen={setOpen}
          value={
            selectedIndex !== undefined
              ? form.getValues(`taskInformation.${selectedIndex}.value`)
              : undefined
          }
          title={t('categoryPage.taskInformation')}
          onSave={(data) => {
            console.log('onSave', selectedIndex, data);

            if (selectedIndex !== undefined) {
              form.setValue(`taskInformation.${selectedIndex}.value`, data);
            } else {
              form.setValue('taskInformation', [
                ...form.getValues('taskInformation'),
                { id: Date.now().toString(), value: data }
              ]);
            }
            setOpen(false);
          }}
        />
      )}
    </>
  );
}
