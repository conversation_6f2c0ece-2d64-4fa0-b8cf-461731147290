import React from 'react';
import { Badge } from '../ui/badge';
import { getBadgeStatusVariant, getStatusDisplayText } from '@/lib/utils';
import { Button } from '../ui/button';

type Props = {
  orderId: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'ACCEPTED' | 'CANCELLED';
  onClick: (status: string) => void;
};

const OrderHeaderDetail: React.FC<Props> = ({ onClick, status, orderId }) => {
  const buttonConfig: {
    [key: string]: { text: string; variant: 'default' | 'destructive' | 'outline' | 'success' };
  } = {
    PENDING: {
      text: 'Confirm Order',
      variant: 'default'
    },
    ACCEPTED: {
      text: 'Completed Order',
      variant: 'success'
    },
    IN_PROGRESS: {
      text: 'Complete Order',
      variant: 'success'
    }
    // COMPLETED: {
    //   text: 'Order Completed',
    //   variant: 'outline'
    // },
    // CANCELLED: {
    //   text: 'Order Cancelled',
    //   variant: 'destructive'
    // }
  };

  return (
    <div className="h-[88px] w-full items-center flex px-6 bg-background">
      <div className="flex space-x-4 items-center">
        <h2 className="text-base font-semibold">
          Order ID <span className="font-bold">#{orderId}</span>
        </h2>
        <div className="h-5 w-px bg-gray-300" />
        <Badge variant={getBadgeStatusVariant(status)} className="rounded-full h-8 px-4 text-sm">
          {getStatusDisplayText(status)}
        </Badge>
      </div>

      <div className="flex items-center gap-3 ml-auto">
        {status === 'PENDING' || status === 'ACCEPTED' || status === 'IN_PROGRESS' ? (
          <Button
            variant={buttonConfig[status].variant}
            onClick={() => onClick(status)}
            className="h-[36px]"
          >
            {buttonConfig[status].text}
          </Button>
        ) : null}
      </div>
    </div>
  );
};

export default OrderHeaderDetail;
