import type { Table } from '@tanstack/react-table';
import SearchBar from '../common/search-bar';
import DateRangePicker, { type DateRangePickerProps } from '../common/date-range-picker';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import IconAssets from '@/asset/icons/icon-assets';

type FinanceProps = {
  id: string;
  orderId: string;
  customerName: string;
  amount: string;
  discount: string;
  serviceFee: string;
  transportFee: string;
  vat: string;
  totalFee: string;
  status: string;
  date: string;
  remark?: string;
};

type Props = {
  table: Table<FinanceProps>;
} & DateRangePickerProps;

export default function FinanceHeader({ table, ...rest }: Props) {
  const { t } = useTranslation();

  const handleExportClicked = () => {
    const rows = table.getFilteredRowModel().rows;
    if (rows.length === 0) {
      alert(t('noDataToExport', 'No data to export'));
      return;
    }

    // Extract headers
    const headers = table
      .getAllColumns()
      .filter((col) => col.getIsVisible())
      .map((col) => col.id);

    // Extract rows
    const csvRows = rows.map((row) =>
      headers.map((header) => JSON.stringify(row.getValue(header) ?? '')).join(',')
    );

    // Create CSV content
    const csvContent = [headers.join(','), ...csvRows].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'finance_export.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex items-center p-4 justify-between">
      {/* Search */}
      <SearchBar
        placeholder={t('searchPlaceholder', 'Search by Order ID or Customer...')}
        value={(table.getColumn('orderId')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('orderId')?.setFilterValue(val)}
      />

      {/* Filters & Export */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Date Range Picker */}
        <DateRangePicker {...rest} />

        {/* Export Button */}
        <Button
          className="bg-[#1964AD] text-white hover:bg-[#3773ad]"
          variant="outline"
          size="sm"
          onClick={handleExportClicked}
        >
          {t('Export')}
          <IconAssets.Export />
        </Button>
      </div>
    </div>
  );
}
