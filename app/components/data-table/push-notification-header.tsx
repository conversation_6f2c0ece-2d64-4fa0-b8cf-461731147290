import type { Table } from '@tanstack/react-table';
import { Select } from '../ui/select';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import DateRangePicker, { type DateRangePickerProps } from '../common/date-range-picker';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { NavLink } from 'react-router';

type Props = {
  table: Table<PushNotificationProps>;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
} & DateRangePickerProps;

export default function PushNotificationHeader({
  table,
  setStatusFilter,
  statusFilter,
  ...rest
}: Props) {
  const { t } = useTranslation();
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder="Search for notification..."
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />
      <div className="flex flex-row gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}></Select>
        <DateRangePicker {...rest} />
        <NavLink to="/push-notification/new-push-notification">
          <Button size="sm">
            {t('header.newPushNotification')} <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
