import React from 'react';
import PaymentLabel from './order-payment-label';
import { Card } from '../ui/card';

type Props = {
  serviceFeeDisplay: string;
  discountDisplay: string;
  serviceFee: string;
  transportFee: string;
  subTotal: string;
  vatFee: string;
  paymentMethod: string;
  totalPayableAmountDisplay: string;
};

const PaymentInfo: React.FC<Props> = (props) => {
  return (
    <Card className="w-[340px] mx-auto bg-card p-6">
      <h2 className="text-base font-bold text-gray-700">Payment Information</h2>
      <div className="space-y-4 border-t pt-4">
        {/* Service */}
        <PaymentLabel label="Service:" value={props.serviceFeeDisplay} />
        <PaymentLabel label="Discount:" value={props.discountDisplay} />
        <PaymentLabel label="Service Fee:" value={props.serviceFee} />
        <PaymentLabel label="Transport Fee:" value={props.transportFee} />
        <PaymentLabel label="SubTotal:" value={props.subTotal} />
        <PaymentLabel label="VAT (10%):" value={props.vatFee} />
        <PaymentLabel label="Payment Method:" value={props.paymentMethod} />
        <div className=" border-t pt-4">
          <span className="font-bold text-gray-900">
            <PaymentLabel label="Total:" value={props.totalPayableAmountDisplay ?? 0} />
          </span>
        </div>
      </div>
    </Card>
  );
};

export default PaymentInfo;
