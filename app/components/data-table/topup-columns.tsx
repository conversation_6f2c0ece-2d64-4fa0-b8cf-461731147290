import type { ColumnDef } from '@tanstack/react-table';
import { Checkbox } from '../ui/checkbox';
import { Button } from '../ui/button';
import { MoreVertical, Pen, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { formatDate } from '@/lib/date-helper';

export const topupColumns: ColumnDef<TopupProps>[] = [
  // Selection checkbox
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },

  // Transaction ID
  {
    accessorKey: 'transactionId',
    header: 'Transaction ID',
    cell: ({ row }) => <div>{row.getValue('transactionId')}</div>
  },

  // Top-up Amount
  {
    accessorKey: 'topupAmount',
    header: 'Top-up Amount',
    cell: ({ row }) => {
      const amount = row.getValue('topupAmount') as number;
      return <div>${amount.toFixed(2)}</div>;
    }
  },

  // Payment Method
  {
    accessorKey: 'paymentMethod',
    header: 'Payment Method',
    cell: ({ row }) => <div>{row.getValue('paymentMethod')}</div>
  },

  // Status (kept)
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <Badge variant={getStatusVariant(status)}>{status}</Badge>;
    }
  },

  // Date (kept)
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      const rawDate = row.getValue('date') as string;
      return <div>{formatDate(rawDate)}</div>;
    }
  },

  // Remark (new)
  {
    accessorKey: 'remark',
    header: 'Remark',
    cell: ({ row }) => <div>{row.getValue('remark')}</div>
  },

  // Actions menu
  {
    id: 'actions',
    enableHiding: false,
    cell: () => {
      return (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="text-blue-500" onClick={() => {}}>
                Edit
                <Pen className="text-blue-500" />
              </DropdownMenuItem>
              <DropdownMenuItem variant="destructive">
                Delete
                <Trash2 />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    }
  }
];

// Updated status badge variants
const getStatusVariant = (status: string) => {
  const lowercaseStatus = status.toLowerCase();
  if (lowercaseStatus === 'completed') return 'approve';
  if (lowercaseStatus === 'pending') return 'warning';
  if (lowercaseStatus === 'cancelled') return 'reject';
  return 'default';
};
