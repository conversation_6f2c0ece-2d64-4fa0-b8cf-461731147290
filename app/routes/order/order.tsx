import OrderComponent from '@/components/common/order-component';
import { useState } from 'react';
import OrderDetailCard from '@/components/common/order-detail-card';
import CategorySearchInterface from '@/components/category/Category-search-interface';
import useOrderQuery from '@/hooks/use-order-query';
import useOrderDetailQuery from '@/hooks/use-order-detail-query';
import { Skeleton } from '@/components/ui/skeleton';
import OrderHeaderDetail from '@/components/common/order-detail-header';
import OrderHeaderLeft from '@/components/order/order-header-left';
import { CustomPagination } from '@/components/common/Custom-Pagination';
import ServiceDetailsV2 from '@/components/common/service-details-v2';
import PaymentInfo from '@/components/common/order-payment';

export default function OrdersList() {
  const [selectedOrderId, setSelectedOrderId] = useState<string | undefined>(undefined);
  const [currentPage, setCurrentPage] = useState(1);

  const [statusFilter, setStatusFilter] = useState('all');
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});

  const { data: ordersData, isPending } = useOrderQuery({
    currentPage: currentPage - 1
  });
  const { data: orderDetailData } = useOrderDetailQuery(selectedOrderId);

  if (isPending)
    return (
      <div className="flex items-center space-x-4 p-6">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
    );

  const ordersArray: OrderListAttributes[] = ordersData?.data || [];

  const selectedOrder =
    ordersArray.find((order: OrderListAttributes) => order.bulkOrderId === selectedOrderId) || null;

  return (
    <div className="flex flex-rows h-screen overflow-hidden">
      <div className="min:w-[300px] max:w-[400px]">
        <OrderHeaderLeft />
        <div className="flex flex-col h-[calc(100vh-88px)]">
          <CategorySearchInterface
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            dateRange={dateRange}
            setDateRange={setDateRange}
            isCalendarOpen={isCalendarOpen}
            setIsCalendarOpen={setIsCalendarOpen}
          />
          <div className="flex flex-col h-full overflow-scroll px-6 gap-2">
            {ordersArray.map((order, index: number) => (
              <div
                key={index}
                className="cursor-pointer"
                onClick={() => order.bulkOrderId && setSelectedOrderId(order.bulkOrderId)}
              >
                <OrderComponent isActive={selectedOrderId === order.bulkOrderId} order={order} />
              </div>
            ))}
          </div>
          <CustomPagination
            currentPage={ordersData?.pagination?.currentPage || 1}
            totalPages={ordersData?.pagination?.totalPages || 1}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
      <div className="flex flex-1 flex-col overflow-hidden items-start bg-muted">
        {selectedOrder && (
          <>
            <OrderHeaderDetail
              status={selectedOrder.status || 'PENDING'}
              orderId={selectedOrder.bulkOrderId}
              onClick={() => {}}
            />
            <div className="w-full overflow-hidden h-[calc(100vh-88px)]">
              <div className="overflow-scroll h-full p-6">
                <OrderDetailCard data={orderDetailData} />
                <div className="flex gap-4 flex-row  pt-4">
                  <div className="flex flex-1 flex-col gap-4">
                    {orderDetailData?.items?.map((service, index) => (
                      <ServiceDetailsV2 index={index} key={index} service={service} />
                    ))}
                  </div>
                  <div>
                    <PaymentInfo
                      serviceFeeDisplay={orderDetailData?.serviceFeeDisplay || ''}
                      discountDisplay={orderDetailData?.discountDisplay || ''}
                      serviceFee={orderDetailData?.serviceFeeDisplay || ''}
                      transportFee={orderDetailData?.transportFeeDisplay || ''}
                      subTotal={orderDetailData?.subTotalDisplay || ''}
                      vatFee={orderDetailData?.vatFeeDisplay || ''}
                      paymentMethod={orderDetailData?.paymentMethodDisplay || ''}
                      totalPayableAmountDisplay={orderDetailData?.totalPayableAmountDisplay || ''}
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
