import React, { useState } from 'react';
import DateRangePicker, { type DateRangePickerProps } from '../common/date-range-picker';
import SearchBar from '../common/search-bar';
import { Combobox } from '../common/combobox';
import { useTranslation } from 'react-i18next';

type Props = {
  statusFilter: string;
  setStatusFilter: (value: string) => void;
} & DateRangePickerProps;

export default function CategorySearchInterface({ setStatusFilter, statusFilter, ...rest }: Props) {
  const [searchValue, setSearchValue] = useState('');
  const { t } = useTranslation();

  return (
    <div className="w-full py-4 px-6">
      <div className="mb-4">
        <SearchBar
          placeholder="Search for order id..."
          value={searchValue}
          onChange={setSearchValue}
        />
      </div>
      <div className="flex gap-4 w-full">
        <Combobox
          placeholder={t('allStatus')}
          data={[
            { label: 'All Status', value: 'all' },
            { label: 'Pending', value: 'Pending' },
            { label: 'Confirmed', value: 'Confirmed' },
            { label: 'Completed', value: 'Completed' }
          ]}
          className="w-[120px]"
          value={statusFilter}
          onSelect={setStatusFilter}
        />

        <DateRangePicker iconHidden className="flex flex-1" {...rest} />
      </div>
    </div>
  );
}
