import React from 'react';
import OrderAddress from '../common/order-address';
import OrderDate from '../common/order-date';
import OrderProfile from '../common/order-profile';
import { phoneNumber } from '@/lib/date-helper';

type OrderDetailCardProps = {
  data?: OrderListAttributes | null;
};

export default function OrderDetailCard({ data }: OrderDetailCardProps) {
  if (!data) return null;

  return (
    <div className="p-6 rounded-xl shadow-sm bg-card">
      <p className="text-xl font-bold mb-6">Detail</p>

      <div className="flex items-start justify-between mb-4">
        <OrderProfile
          fullname={data.fullname}
          phone={data.phone ? phoneNumber(data.phone) : ''}
          profileUrl={data.profileUrl}
          paymentStatus={data.paymentStatus}
        />
      </div>

      <div className="flex justify-between text-sm text-[#4B5563] mx-6">
        <OrderAddress userAddress={data.UserAddress[0]} />
        <OrderDate scheduleDate={data.scheduleDate} />
      </div>
    </div>
  );
}
