import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useOrderDetailQuery(bulkOrderId?: string) {
  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.ORDER_DETAIL, bulkOrderId],
    queryFn: (): Promise<OrderListAttributes | null> =>
      api.get(`${API_ENDPOINT.ORDER_DETAIL}/${bulkOrderId}/detail`),
    enabled: !!bulkOrderId
  });

  return query;
}
