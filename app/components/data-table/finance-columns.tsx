import type { ColumnDef } from '@tanstack/react-table';
import { Checkbox } from '../ui/checkbox';
import { Button } from '../ui/button';
import { MoreVertical, Pen, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { formatDate } from '@/lib/date-helper';

// ✅ FinanceProps from your provided type
type FinanceProps = {
  id: string;
  orderId: string;
  customerName: string;
  profileUrl?: string;
  amount: string;
  discount: string;
  serviceFee: string;
  transportFee: string;
  vat: string;
  totalFee: string;
  status: string;
  date: string;
  remark?: string;
};

export const financeColumns: ColumnDef<FinanceProps>[] = [
  // ✅ Selection Checkbox
  {
    id: 'select',
    meta: {
      isSticky: true,
      stickyLeft: 0
    },
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },

  // ✅ Order ID
  {
    accessorKey: 'orderId',
    header: 'Order ID',
    cell: ({ row }) => <div className="w-[180px]">{row.getValue('orderId')}</div>
  },

  // ✅ Customer Name
  {
    accessorKey: 'customerName',
    header: 'Customer Name',
    cell: ({ row }) => {
      const customerName = row.getValue('customerName') as string;
      const profileUrl = row.original.profileUrl || '/default-profile.png'; // fallback image

      return (
        <div className="flex items-center gap-2 w-[220px]">
          <img
            src={profileUrl}
            alt={customerName}
            className="w-10 h-10 rounded-full object-cover"
          />
          <span>{customerName}</span>
        </div>
      );
    }
  },

  // ✅ Amount
  {
    accessorKey: 'amount',
    header: 'Amount',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('amount') as string);
      return <div className="w-[160px]">${value.toFixed(2)}</div>;
    }
  },

  // ✅ Discount
  {
    accessorKey: 'discount',
    header: 'Discount',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('discount') as string);
      return <div className="w-[160px]">${value.toFixed(2)}</div>;
    }
  },

  // ✅ Service Fee
  {
    accessorKey: 'serviceFee',
    header: 'Service Fee',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('serviceFee') as string);
      return <div className="w-[160px]">${value.toFixed(2)}</div>;
    }
  },

  // ✅ Transport Fee
  {
    accessorKey: 'transportFee',
    header: 'Transport Fee',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('transportFee') as string);
      return <div className="w-[160px]">${value.toFixed(2)}</div>;
    }
  },

  // ✅ VAT
  {
    accessorKey: 'vat',
    header: 'VAT',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('vat') as string);
      return <div className="w-[160px]">${value.toFixed(2)}</div>;
    }
  },

  // ✅ Total Fee
  {
    accessorKey: 'totalFee',
    header: 'Total Fee',
    cell: ({ row }) => {
      const value = parseFloat(row.getValue('totalFee') as string);
      return <div className="font-semibold w-[160px] ">${value.toFixed(2)}</div>;
    }
  },

  // ✅ Status
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return (
        <div className="w-[160px]">
          <Badge variant={getStatusVariant(status)}>{status}</Badge>
        </div>
      );
    }
  },

  // ✅ Date
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      const rawDate = row.getValue('date') as string;
      return <div className="w-[180px]">{formatDate(rawDate)}</div>;
    }
  },

  // ✅ Remark
  {
    accessorKey: 'remark',
    header: 'Remark',
    cell: ({ row }) => <div className="w-[240px]">{row.getValue('remark')}</div>
  },

  // ✅ Actions
  {
    id: 'actions',
    enableHiding: true,
    meta: {
      isSticky: true,
      stickyRight: 0
    },
    cell: () => {
      return (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="text-blue-500" onClick={() => {}}>
                Edit
                <Pen className="text-blue-500" />
              </DropdownMenuItem>
              <DropdownMenuItem variant="destructive">
                Delete
                <Trash2 />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    }
  }
];

// ✅ Status badge styles
const getStatusVariant = (status: string) => {
  const lowercaseStatus = status.toLowerCase();
  if (lowercaseStatus === 'paid') return 'approve';
  if (lowercaseStatus === 'pending') return 'warning';
  if (lowercaseStatus === 'cancelled') return 'reject';
  return 'default';
};
