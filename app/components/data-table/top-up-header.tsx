import type { Table } from '@tanstack/react-table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import SearchBar from '../common/search-bar';
import DateRangePicker, { type DateRangePickerProps } from '../common/date-range-picker';
import { useTranslation } from 'react-i18next';

type Props = {
  table: Table<TopupProps>;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
} & DateRangePickerProps;

export default function TopupHeader({ table, setStatusFilter, statusFilter, ...rest }: Props) {
  const { t } = useTranslation();

  return (
    <div className="flex items-center p-4 justify-between">
      {/* Search */}
      <SearchBar
        placeholder={t('searchPlaceholder', 'Search for transactions id...')}
        value={(table.getColumn('transactionId')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('transactionId')?.setFilterValue(val)}
      />

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Status Filter */}
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('filterByStatus', 'Filter by status')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allStatus', 'All Status')}</SelectItem>
            <SelectItem value="Completed">{t('completed', 'Completed')}</SelectItem>
            <SelectItem value="Pending">{t('pending', 'Pending')}</SelectItem>
            <SelectItem value="Cancelled">{t('cancelled', 'Cancelled')}</SelectItem>
          </SelectContent>
        </Select>

        {/* Date Range Picker */}
        <DateRangePicker {...rest} />
      </div>
    </div>
  );
}
