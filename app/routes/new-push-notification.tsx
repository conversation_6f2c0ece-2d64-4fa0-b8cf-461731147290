import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useWatch } from 'react-hook-form';
import CustomSelect from '@/components/common/custom-select';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';
import { ValidFromDatePicker } from '@/components/common/single-date-picker';
import {
  pushNotificationSchema,
  type PushNotificationSchemaProps
} from '@/lib/schema/push-notification-schema';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

export default function NewPushNotification() {
  const { t } = useTranslation();

  const form = useForm<PushNotificationSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(pushNotificationSchema),
    defaultValues: {
      name: { en: '', km: '', vn: '', zh: '', cn: '' },
      description: '',
      status: 'Active',
      targetUser: 'General User',
      notificationType: 'General',
      validFrom: '',
      validTo: ''
    }
  });

  const notificationType = useWatch({ control: form.control, name: 'notificationType' });

  const onSubmit = async (values: PushNotificationSchemaProps) => {
    console.log('Form submitted with values:', values);
  };

  return (
    <div>
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />

      <ContentWrapper>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="p-6 flex flex-row gap-6 items-baseline"
          >
            {/* Left side */}
            <Card className="flex flex-1">
              <CardHeader className="gap-4">
                <CardTitle>{t('productPage.details')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                  <FormInputMultipleLanguages
                    form={form}
                    name="name"
                    label={t('Name')}
                    placeholder={t('Name')}
                  />

                  {/* Notification Type */}
                  <FormField
                    control={form.control}
                    name="notificationType"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('Notification Type')}</FormLabel>
                        <CustomSelect
                          className="!h-10"
                          placeholder={t('Select Notification Type')}
                          data={[
                            { label: 'General', value: 'General' },
                            { label: 'Promotion', value: 'Promotion' }
                          ]}
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Target User */}
                  <FormField
                    control={form.control}
                    name="targetUser"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('Targeted Users')}</FormLabel>
                        <CustomSelect
                          className="!h-10"
                          placeholder={t('Select Targeted Users')}
                          data={[
                            { label: 'General User', value: 'General User' },
                            { label: 'Elderly', value: 'Elderly' }
                          ]}
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status */}
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('Status')}</FormLabel>
                        <CustomSelect
                          className="!h-10"
                          placeholder={t('Status')}
                          data={[
                            { label: 'Active', value: 'Active' },
                            { label: 'Inactive', value: 'Inactive' },
                            { label: 'Pending', value: 'Pending' }
                          ]}
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Buddle Type - show only if Promotion */}
                  {notificationType === 'Promotion' && (
                    <FormField
                      control={form.control}
                      name="buddleType"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{t('Bundle Type')}</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder={t('Select Bundle Type')}
                            data={[
                              {
                                label: 'Bundle Deals - More Saves, More Services!',
                                value: 'Bundle Deals - More Saves, More Services!'
                              },
                              { label: 'buy-more-pay-less', value: 'buy-more-pay-less' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="mt-4">
                  <Label htmlFor="description" className="mb-2 block">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Enter description"
                    className="w-full h-[100PX] rounded-[6px]"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Right side */}
            <div className="w-[368px] flex flex-col gap-4 h-full">
              <Card className="flex-1">
                <CardHeader className="gap-4 flex-col">
                  <CardTitle>{t('Schedule Date & Time')}</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col gap-4">
                  <FormField
                    control={form.control}
                    name="validFrom"
                    render={() => (
                      <ValidFromDatePicker
                        control={form.control}
                        name="validFrom"
                        label="Active From"
                        placeholder="Choose start date"
                      />
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="validTo"
                    render={() => (
                      <ValidFromDatePicker
                        control={form.control}
                        name="validTo"
                        label="Active To"
                        placeholder="Choose end date"
                      />
                    )}
                  />
                </CardContent>
              </Card>
            </div>
          </form>
        </Form>
      </ContentWrapper>
    </div>
  );
}
