import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import CustomSelect from '@/components/common/custom-select';
import DragDropFileUpload from '@/components/common/drag-drop-file-upload';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import FormInput from '@/components/common/form-input';
import { bannerSchema, type BannerSchemaProps } from '@/lib/schema/banner-schema';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';

export default function NewBanner() {
  const { t } = useTranslation();
  const form = useForm<BannerSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(bannerSchema),
    defaultValues: {
      name: {
        en: '',
        km: '',
        vn: '',
        zh: '',
        cn: ''
      },
      status: 'Active',
      url: '',
      attachments: []
    }
  });

  const onSubmit = (values: BannerSchemaProps) => {
    console.log('Banner form submitted:', values);
    // Call your API or mutation here
  };

  return (
    <div>
      {/* Header with Save Button */}
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />

      <ContentWrapper>
        <div className="p-6 flex flex-row gap-6 items-baseline">
          <Card className="flex flex-1">
            <CardHeader className="gap-4">
              <CardTitle>{t('Details')}</CardTitle>
            </CardHeader>

            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-3">
                    {/* Name */}
                    <FormInputMultipleLanguages
                      form={form}
                      name="name"
                      label="Name"
                      placeholder="Name"
                    />

                    {/* Status */}
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{t('Status')}</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder={t('Select status')}
                            data={[
                              { label: 'Active', value: 'Active' },
                              { label: 'Inactive', value: 'Inactive' },
                              { label: 'Pending', value: 'Pending' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* URL */}
                    <FormInput
                      control={form.control}
                      name="url"
                      label={t('URL')}
                      placeholder={t('Enter banner link')}
                    />
                  </div>

                  {/* File Upload */}
                  <div className="mt-4">
                    <DragDropFileUpload />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </ContentWrapper>
    </div>
  );
}
