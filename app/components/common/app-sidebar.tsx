import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem
} from '../ui/sidebar';
import {
  DashboardSquare01Icon,
  ShoppingCart01Icon
  // PromotionIcon,
  // SaveMoneyDollarIcon,
  // Settings01Icon
} from 'hugeicons-react';
import beasyIcon from '@/asset/images/beasy-icon.png';
import { NavLink, useLocation } from 'react-router';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import IconAssets from '@/asset/icons/icon-assets';

// Menu items.
const items = [
  {
    title: 'appSidebar.dashboard',
    url: '/',
    icon: DashboardSquare01Icon
  },
  {
    title: 'appSidebar.order',
    url: '/order',
    icon: ShoppingCart01Icon
  },
  {
    title: 'appSidebar.services',
    icon: IconAssets.Services,
    items: [
      {
        title: 'appSidebar.category',
        url: '/category',
        isActive: true
      },
      {
        title: 'appSidebar.categoryAddon',
        url: '/category-addon'
      },
      {
        title: 'appSidebar.product',
        url: '/product'
      },
      {
        title: 'appSidebar.productOption',
        url: '/product-option'
      }
    ]
  }
  // {
  //   title: 'appSidebar.marketing',
  //   icon: PromotionIcon,
  //   items: [
  //     {
  //       title: 'appSidebar.customer',
  //       url: '/customer',
  //       isActive: true
  //     },
  //     {
  //       title: 'appSidebar.serviceBundle',
  //       url: '/service-bundle'
  //     },
  //     {
  //       title: 'appSidebar.topUp',
  //       url: '/top-up'
  //     },
  //     {
  //       title: 'appSidebar.voucher',
  //       url: '/voucher'
  //     },
  //     {
  //       title: 'appSidebar.promotions',
  //       url: '/promotions'
  //     },
  //     {
  //       title: 'appSidebar.referralProgram',
  //       url: '/referral-program'
  //     },
  //     {
  //       title: 'appSidebar.pushNotification',
  //       url: '/push-notification'
  //     },
  //     {
  //       title: 'appSidebar.banner',
  //       url: '/banner'
  //     }
  //   ]
  // },
  // {
  //   title: 'appSidebar.finance',
  //   url: '/finance',
  //   icon: SaveMoneyDollarIcon
  // },
  // {
  //   title: 'appSidebar.setup',
  //   icon: Settings01Icon,
  //   items: [
  //     {
  //       title: 'appSidebar.users',
  //       url: '/users',
  //       isActive: true
  //     },
  //     {
  //       title: 'appSidebar.roles',
  //       url: '/roles'
  //     }
  //   ]
  // }
];
export function AppSidebar() {
  const { t } = useTranslation();
  const location = useLocation();

  return (
    <Sidebar variant="inset" className="p-0">
      <SidebarContent className="p-6 bg-background">
        <SidebarGroup className="gap-6 p-0">
          <div className="flex flex-row items-center gap-3 h-[72px]">
            <img src={beasyIcon} className="w-12 h-12" />
            <SidebarGroupLabel>bEasy</SidebarGroupLabel>
          </div>
          <SidebarGroupContent>
            <SidebarMenu className="gap-2">
              {items.map((item) => {
                if (item.items) {
                  return (
                    <Collapsible key={item.title} className="group/collapsible">
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton size="lg" className="gap-2">
                            <item.icon />
                            <div className="flex-1">
                              <span>{t(item.title)}</span>
                            </div>
                            <ChevronDown className="h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub className="border-l-0 ml-0 pl-0 pr-0 mx-0">
                            {item.items.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton
                                  className="px-10"
                                  asChild
                                  isActive={getBasePath(location.pathname) === subItem.url}
                                >
                                  <NavLink to={subItem.url}>
                                    <span>{t(subItem.title)}</span>
                                  </NavLink>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  );
                }
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      size="lg"
                      isActive={getBasePath(location.pathname) === item.url}
                    >
                      <NavLink to={item.url}>
                        <item.icon />
                        <span>{t(item.title)}</span>
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
function getBasePath(urlPath: string) {
  const secondSlashIndex = urlPath.indexOf('/', 1);
  if (secondSlashIndex !== -1) return urlPath.substring(0, secondSlashIndex);
  return urlPath;
}
